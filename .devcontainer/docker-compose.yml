services:
  sentry:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    volumes:
      - ..:/workspace/sentry:cached
      - e2e_logs:/workspace/sentry/log
    command: sleep infinity
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]
    depends_on:
      - redis

  sentry-test:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    volumes:
      - ..:/workspace/sentry:cached
      - e2e_logs:/workspace/sentry/log
    working_dir: /workspace/sentry
    entrypoint: .devcontainer/setup
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]
    depends_on:
      - redis

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"

  sentry-rails-mini:
    build:
      context: .
      dockerfile: Dockerfile
      target: rails-mini
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    working_dir: /workspace/sentry/spec/apps/rails-mini
    volumes:
      - ..:/workspace/sentry:cached
      - e2e_logs:/workspace/sentry/log
      - rails_gems:/workspace/gems
    ports:
      - "5000:5000"
    command: ruby app.rb
    environment:
      - RAILS_ENV=development
    profiles:
      - e2e
    env_file: [".env"]

  sentry-svelte-mini:
    build:
      context: .
      dockerfile: Dockerfile
      target: svelte-mini
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    working_dir: /workspace/sentry/spec/apps/svelte-mini
    volumes:
      - ..:/workspace/sentry:cached
      - svelte_node_modules:/workspace/sentry/spec/apps/svelte-mini/node_modules
    ports:
      - "5001:5001"
    command: npm run dev -- --host 0.0.0.0
    environment:
      - SENTRY_E2E_RAILS_APP_URL=http://sentry-rails-mini:5000
    profiles:
      - e2e
    env_file: [".env"]

volumes:
  e2e_logs:
  rails_gems:
  svelte_node_modules:
