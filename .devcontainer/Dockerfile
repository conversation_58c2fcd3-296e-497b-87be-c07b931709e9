ARG IMAGE="ruby"
ARG VERSION="3.4.5"
ARG DISTRO="slim-bookworm"

# Rails mini stage
FROM ${IMAGE}:${VERSION}-${DISTRO} AS rails-mini

USER root
RUN apt-get update && apt-get install -y --no-install-recommends \
  sudo \
  gnupg \
  git \
  curl \
  wget \
  build-essential \
  pkg-config \
  libssl-dev \
  libreadline-dev \
  zlib1g-dev \
  autoconf \
  bison \
  libyaml-dev \
  libncurses5-dev \
  libffi-dev \
  libgdbm-dev \
  sqlite3 \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

RUN groupadd --gid 1000 sentry \
  && useradd --uid 1000 --gid sentry --shell /bin/bash --create-home sentry

# Create entrypoint script for rails mini to install gems on startup
RUN echo '#!/bin/bash\nset -e\necho "Installing Ruby dependencies..."\ncd /workspace/sentry\nbundle install --gemfile=sentry-ruby/Gemfile\nbundle install --gemfile=sentry-rails/Gemfile\ncd /workspace/sentry/spec/apps/rails-mini\nexec "$@"' > /usr/local/bin/rails-entrypoint.sh && \
    chmod +x /usr/local/bin/rails-entrypoint.sh

WORKDIR /workspace/sentry
RUN chown -R sentry:sentry /workspace/sentry
RUN mkdir /workspace/gems && chown -R sentry:sentry /workspace/gems

ARG VERSION
ARG GEM_HOME="/workspace/gems/${VERSION}"

ENV LANG=C.UTF-8 \
  BUNDLE_JOBS=4 \
  BUNDLE_RETRY=3 \
  GEM_HOME=/workspace/gems/${VERSION} \
  PATH=$PATH:${GEM_HOME}/bin \
  REDIS_HOST=redis

USER sentry

# Copy source code
COPY --chown=sentry:sentry . .

ENTRYPOINT ["/usr/local/bin/rails-entrypoint.sh"]

# Svelte mini stage
FROM ${IMAGE}:${VERSION}-${DISTRO} AS svelte-mini

USER root
RUN apt-get update && apt-get install -y --no-install-recommends \
  sudo \
  git \
  curl \
  nodejs \
  npm \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

RUN groupadd --gid 1000 sentry \
  && useradd --uid 1000 --gid sentry --shell /bin/bash --create-home sentry

# Create entrypoint script to install npm dependencies on startup
RUN echo '#!/bin/bash\nset -e\necho "Installing npm dependencies..."\nsudo chown -R sentry:sentry /workspace/sentry/spec/apps/svelte-mini/node_modules\nnpm install\nexec "$@"' > /usr/local/bin/svelte-entrypoint.sh && \
    chmod +x /usr/local/bin/svelte-entrypoint.sh

# Allow sentry user to use sudo without password for chown
RUN echo "sentry ALL=(ALL) NOPASSWD: /bin/chown" >> /etc/sudoers

WORKDIR /workspace/sentry
RUN chown -R sentry:sentry /workspace/sentry

USER sentry

# Copy source code
COPY --chown=sentry:sentry . .

ENTRYPOINT ["/usr/local/bin/svelte-entrypoint.sh"]
